#!/usr/bin/env python3
"""
Test Script for Torch.Classes Fix

This script tests whether the torch.classes RuntimeError fix is working
by simulating the import order and configuration that caused the original issue.

Usage:
    python test_torch_fix.py
"""

import sys
import os
from pathlib import Path

def test_original_problem():
    """Test the original problematic import order"""
    print("🧪 Testing original problematic import order...")
    
    try:
        # This would cause the original error
        import streamlit as st
        print("❌ Streamlit imported without configuration - this could cause issues")
        
        # Try to import torch-dependent modules
        sys.path.append(str(Path(__file__).parent))
        from app.workflows.rag_workflow import RAGWorkflow
        print("❌ RAGWorkflow imported after Streamlit - this could trigger torch.classes error")
        
        return False
    except Exception as e:
        print(f"❌ Original problem reproduced: {e}")
        return True

def test_fixed_import_order():
    """Test the fixed import order"""
    print("\n🔧 Testing fixed import order...")
    
    try:
        # First configure environment
        os.environ['STREAMLIT_SERVER_FILE_WATCHER_TYPE'] = 'none'
        os.environ['TORCH_LOGS'] = ''  # Empty string to disable logging
        print("✅ Environment variables set")
        
        # Import and configure Streamlit first
        import streamlit as st
        from streamlit import config as st_config
        
        # Configure Streamlit to prevent file watching
        st_config.set_option('server.fileWatcherType', 'none')
        st_config.set_option('server.runOnSave', False)
        st_config.set_option('runner.magicEnabled', False)
        st_config.set_option('global.developmentMode', False)
        print("✅ Streamlit configured before torch imports")
        
        # Now import torch-dependent modules
        sys.path.append(str(Path(__file__).parent))
        
        # Test lazy import pattern
        RAGWorkflow = None
        
        def lazy_import():
            global RAGWorkflow
            if RAGWorkflow is None:
                from app.workflows.rag_workflow import RAGWorkflow
                print("✅ RAGWorkflow imported lazily")
            return RAGWorkflow
        
        # Test the lazy import
        workflow_class = lazy_import()
        print("✅ Lazy import successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Fixed import order failed: {e}")
        return False

def test_streamlit_config():
    """Test Streamlit configuration module"""
    print("\n⚙️ Testing streamlit_config module...")
    
    try:
        from streamlit_config import configure_streamlit_early, setup_torch_environment
        
        # Test configuration functions
        config_result = configure_streamlit_early()
        setup_torch_environment()
        
        if config_result:
            print("✅ streamlit_config module works correctly")
            return True
        else:
            print("⚠️ streamlit_config module has warnings but works")
            return True
            
    except Exception as e:
        print(f"❌ streamlit_config module failed: {e}")
        return False

def test_torch_import():
    """Test that torch can be imported without issues"""
    print("\n🔥 Testing torch import...")
    
    try:
        import torch
        print(f"✅ Torch imported successfully (version: {torch.__version__})")
        
        # Test torch.classes specifically
        if hasattr(torch, 'classes'):
            print("✅ torch.classes exists")
            
            # Try to access the problematic attribute
            try:
                if hasattr(torch.classes, '__path__'):
                    path_attr = torch.classes.__path__
                    print(f"✅ torch.classes.__path__ accessible: {type(path_attr)}")
                else:
                    print("ℹ️ torch.classes.__path__ doesn't exist (this is normal)")
                    
            except Exception as e:
                print(f"⚠️ torch.classes.__path__ access issue: {e}")
        else:
            print("ℹ️ torch.classes doesn't exist (this is normal in some versions)")
            
        return True
        
    except Exception as e:
        print(f"❌ Torch import failed: {e}")
        return False

def test_environment_variables():
    """Test that environment variables are set correctly"""
    print("\n🌍 Testing environment variables...")
    
    expected_vars = {
        'STREAMLIT_SERVER_FILE_WATCHER_TYPE': 'none',
        'TORCH_LOGS': 'off'
    }
    
    all_set = True
    for var, expected_value in expected_vars.items():
        actual_value = os.environ.get(var)
        if actual_value == expected_value:
            print(f"✅ {var}={actual_value}")
        else:
            print(f"❌ {var}={actual_value} (expected: {expected_value})")
            all_set = False
    
    return all_set

def main():
    """Run all tests"""
    print("🚀 Testing Torch.Classes RuntimeError Fix")
    print("=" * 50)
    
    tests = [
        ("Environment Variables", test_environment_variables),
        ("Streamlit Configuration", test_streamlit_config),
        ("Torch Import", test_torch_import),
        ("Fixed Import Order", test_fixed_import_order),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The torch.classes fix is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the configuration.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
