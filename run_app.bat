@echo off
REM Streamlit Application Launcher for Windows
REM This batch file configures the environment and launches the Streamlit app
REM with settings to prevent torch.classes and event loop issues.

echo ========================================
echo  Streamlit Application Launcher
echo  Fixing torch.classes RuntimeError
echo ========================================

REM Set environment variables to prevent torch and Streamlit issues
set STREAMLIT_SERVER_FILE_WATCHER_TYPE=none
set STREAMLIT_SERVER_RUN_ON_SAVE=false
set STREAMLIT_RUNNER_MAGIC_ENABLED=false
set STREAMLIT_GLOBAL_DEVELOPMENT_MODE=false
set STREAMLIT_BROWSER_GATHER_USAGE_STATS=false

REM Torch configuration to prevent path inspection issues
set TORCH_LOGS=
set TORCH_SHOW_CPP_STACKTRACES=0
set TORCH_DISTRIBUTED_DEBUG=OFF
set TORCH_CUDNN_V8_API_DISABLED=1
set TORCH_PROFILER_ENABLED=0

REM Python configuration
set PYTHONUNBUFFERED=1
set PYTHONIOENCODING=utf-8
set PYTHONASYNCIODEBUG=0

echo Environment variables configured successfully.
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again.
    pause
    exit /b 1
)

REM Check if the main application file exists
if not exist "app\main.py" (
    echo ERROR: Application file app\main.py not found
    echo Please ensure you are running this from the project root directory.
    pause
    exit /b 1
)

echo Starting Streamlit application...
echo Application will be available at: http://localhost:8501
echo.
echo To stop the application, press Ctrl+C
echo ========================================

REM Launch the Streamlit application with configuration
python -m streamlit run app\main.py ^
    --server.port 8501 ^
    --server.address localhost ^
    --server.fileWatcherType none ^
    --server.runOnSave false ^
    --runner.magicEnabled false ^
    --global.developmentMode false ^
    --server.enableCORS false ^
    --server.enableXsrfProtection false

REM Check if the application exited with an error
if errorlevel 1 (
    echo.
    echo ERROR: Application failed to start or exited with an error.
    echo Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo Application stopped.
pause
